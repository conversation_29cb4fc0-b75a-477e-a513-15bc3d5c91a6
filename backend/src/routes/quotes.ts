import express from 'express';
import { saveQuote, getRandomQuote, getAllQuotes, deleteQuote, getQuoteById, Quote } from '../database';

const router = express.Router();

// POST /api/quotes - Save a new quote
router.post('/', async (req, res) => {
  try {
    const { text, author } = req.body;

    // Validation
    if (!text || !author) {
      return res.status(400).json({ 
        message: 'Both text and author are required' 
      });
    }

    if (typeof text !== 'string' || typeof author !== 'string') {
      return res.status(400).json({ 
        message: 'Text and author must be strings' 
      });
    }

    if (text.trim().length === 0 || author.trim().length === 0) {
      return res.status(400).json({ 
        message: 'Text and author cannot be empty' 
      });
    }

    if (text.length > 1000) {
      return res.status(400).json({ 
        message: 'Quote text cannot exceed 1000 characters' 
      });
    }

    if (author.length > 100) {
      return res.status(400).json({ 
        message: 'Author name cannot exceed 100 characters' 
      });
    }

    const quoteId = await saveQuote({ 
      text: text.trim(), 
      author: author.trim() 
    });

    res.status(201).json({ 
      message: 'Quote saved successfully', 
      id: quoteId 
    });

  } catch (error) {
    console.error('Error saving quote:', error);
    res.status(500).json({ 
      message: 'Failed to save quote' 
    });
  }
});

// GET /api/quotes/random - Get a random quote
router.get('/random', async (req, res) => {
  try {
    const quote = await getRandomQuote();

    if (!quote) {
      return res.status(404).json({ 
        message: 'No quotes found' 
      });
    }

    res.json({ quote });

  } catch (error) {
    console.error('Error fetching random quote:', error);
    res.status(500).json({ 
      message: 'Failed to fetch quote' 
    });
  }
});

// GET /api/quotes - Get all quotes
router.get('/', async (req, res) => {
  try {
    const quotes = await getAllQuotes();
    res.json({ quotes, count: quotes.length });

  } catch (error) {
    console.error('Error fetching quotes:', error);
    res.status(500).json({
      message: 'Failed to fetch quotes'
    });
  }
});

// DELETE /api/quotes/:id - Delete a specific quote
router.delete('/:id', async (req, res) => {
  try {
    const id = parseInt(req.params.id);

    // Validation
    if (isNaN(id) || id <= 0) {
      return res.status(400).json({
        message: 'Invalid quote ID'
      });
    }

    // Check if quote exists
    const existingQuote = await getQuoteById(id);
    if (!existingQuote) {
      return res.status(404).json({
        message: 'Quote not found'
      });
    }

    // Delete the quote
    const deleted = await deleteQuote(id);

    if (deleted) {
      res.json({
        message: 'Quote deleted successfully',
        deletedQuote: existingQuote
      });
    } else {
      res.status(500).json({
        message: 'Failed to delete quote'
      });
    }

  } catch (error) {
    console.error('Error deleting quote:', error);
    res.status(500).json({
      message: 'Failed to delete quote'
    });
  }
});

export default router;
