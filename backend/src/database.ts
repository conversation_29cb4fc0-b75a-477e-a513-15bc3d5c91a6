import sqlite3 from 'sqlite3';
import path from 'path';

const dbPath = path.join(__dirname, '..', 'data', 'quotes.db');

// Create database connection
export const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('Error opening database:', err.message);
  } else {
    console.log('Connected to SQLite database');
  }
});

// Initialize database schema
export const initializeDatabase = () => {
  const createQuotesTable = `
    CREATE TABLE IF NOT EXISTS quotes (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      text TEXT NOT NULL,
      author TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `;

  db.run(createQuotesTable, (err) => {
    if (err) {
      console.error('Error creating quotes table:', err.message);
    } else {
      console.log('Quotes table ready');
    }
  });
};

// Quote interface
export interface Quote {
  id?: number;
  text: string;
  author: string;
  created_at?: string;
}

// Database operations
export const saveQuote = (quote: Omit<Quote, 'id' | 'created_at'>): Promise<number> => {
  return new Promise((resolve, reject) => {
    const sql = 'INSERT INTO quotes (text, author) VALUES (?, ?)';
    db.run(sql, [quote.text, quote.author], function(err) {
      if (err) {
        reject(err);
      } else {
        resolve(this.lastID);
      }
    });
  });
};

export const getRandomQuote = (): Promise<Quote | null> => {
  return new Promise((resolve, reject) => {
    const sql = 'SELECT * FROM quotes ORDER BY RANDOM() LIMIT 1';
    db.get(sql, [], (err, row: Quote) => {
      if (err) {
        reject(err);
      } else {
        resolve(row || null);
      }
    });
  });
};

export const getAllQuotes = (): Promise<Quote[]> => {
  return new Promise((resolve, reject) => {
    const sql = 'SELECT * FROM quotes ORDER BY created_at DESC';
    db.all(sql, [], (err, rows: Quote[]) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
};
