{"name": "quote-app-backend", "version": "1.0.0", "description": "Backend API for Quote Management App", "main": "dist/index.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest"}, "keywords": ["quotes", "api", "express", "typescript"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "sqlite3": "^5.1.6", "dotenv": "^16.3.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/node": "^20.10.5", "typescript": "^5.3.3", "ts-node-dev": "^2.0.0"}}