import { useState, useEffect } from 'react'

interface Quote {
  id: number
  text: string
  author: string
  created_at: string
}

const QuoteManager = () => {
  const [quotes, setQuotes] = useState<Quote[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info' | null; text: string }>({ type: null, text: '' })
  const [deleteConfirm, setDeleteConfirm] = useState<{ show: boolean; quote: Quote | null }>({ show: false, quote: null })

  const fetchQuotes = async () => {
    setIsLoading(true)
    setMessage({ type: null, text: '' })

    try {
      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001'
      const response = await fetch(`${apiUrl}/api/quotes`)

      if (response.ok) {
        const data = await response.json()
        setQuotes(data.quotes || [])
        if (data.quotes.length === 0) {
          setMessage({ type: 'info', text: 'No quotes found. Add some quotes first!' })
        }
      } else {
        const errorData = await response.json()
        setMessage({ type: 'error', text: errorData.message || 'Failed to fetch quotes' })
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Network error. Please try again.' })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteClick = (quote: Quote) => {
    setDeleteConfirm({ show: true, quote })
  }

  const handleDeleteConfirm = async () => {
    if (!deleteConfirm.quote) return

    try {
      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001'
      const response = await fetch(`${apiUrl}/api/quotes/${deleteConfirm.quote.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setMessage({ type: 'success', text: 'Quote deleted successfully!' })
        // Remove the deleted quote from the local state
        setQuotes(quotes.filter(q => q.id !== deleteConfirm.quote!.id))
      } else {
        const errorData = await response.json()
        setMessage({ type: 'error', text: errorData.message || 'Failed to delete quote' })
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Network error. Please try again.' })
    } finally {
      setDeleteConfirm({ show: false, quote: null })
    }
  }

  const handleDeleteCancel = () => {
    setDeleteConfirm({ show: false, quote: null })
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Load quotes when component mounts
  useEffect(() => {
    fetchQuotes()
  }, [])

  return (
    <div className="quote-manager">
      <h2>Manage Quotes</h2>
      
      <button 
        onClick={fetchQuotes}
        className="refresh-btn"
        disabled={isLoading}
      >
        {isLoading ? 'Loading...' : 'Refresh Quotes'}
      </button>

      {message.type && (
        <div className={`message ${message.type}`}>
          {message.text}
        </div>
      )}

      {quotes.length > 0 && (
        <div className="quotes-list">
          <div className="quotes-header">
            <span>Total Quotes: {quotes.length}</span>
          </div>
          
          {quotes.map((quote) => (
            <div key={quote.id} className="quote-item">
              <div className="quote-content">
                <div className="quote-text">"{quote.text}"</div>
                <div className="quote-author">— {quote.author}</div>
                <div className="quote-date">Added: {formatDate(quote.created_at)}</div>
              </div>
              <div className="quote-actions">
                <button 
                  onClick={() => handleDeleteClick(quote)}
                  className="delete-btn"
                  title="Delete this quote"
                >
                  Delete
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {deleteConfirm.show && deleteConfirm.quote && (
        <div className="modal-overlay">
          <div className="modal">
            <h3>Confirm Delete</h3>
            <p>Are you sure you want to delete this quote?</p>
            <div className="quote-preview">
              <div className="quote-text">"{deleteConfirm.quote.text}"</div>
              <div className="quote-author">— {deleteConfirm.quote.author}</div>
            </div>
            <div className="modal-actions">
              <button onClick={handleDeleteCancel} className="cancel-btn">
                Cancel
              </button>
              <button onClick={handleDeleteConfirm} className="confirm-delete-btn">
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default QuoteManager
