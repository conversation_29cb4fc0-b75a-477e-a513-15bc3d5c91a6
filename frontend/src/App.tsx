import { useState } from 'react'
import './App.css'
import QuoteForm from './components/QuoteForm'
import RandomQuote from './components/RandomQuote'
import QuoteManager from './components/QuoteManager'

function App() {
  const [activeTab, setActiveTab] = useState<'add' | 'view' | 'manage'>('add')

  return (
    <div className="App">
      <header className="App-header">
        <h1>Quote Management App</h1>
        <nav className="tab-nav">
          <button
            className={activeTab === 'add' ? 'active' : ''}
            onClick={() => setActiveTab('add')}
          >
            Add Quote
          </button>
          <button
            className={activeTab === 'view' ? 'active' : ''}
            onClick={() => setActiveTab('view')}
          >
            View Random Quote
          </button>
          <button
            className={activeTab === 'manage' ? 'active' : ''}
            onClick={() => setActiveTab('manage')}
          >
            Manage Quotes
          </button>
        </nav>
      </header>
      
      <main className="App-main">
        {activeTab === 'add' && <QuoteForm />}
        {activeTab === 'view' && <RandomQuote />}
        {activeTab === 'manage' && <QuoteManager />}
      </main>
    </div>
  )
}

export default App
