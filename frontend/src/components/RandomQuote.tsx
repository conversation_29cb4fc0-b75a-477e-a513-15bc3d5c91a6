import { useState } from 'react'

interface Quote {
  id: number
  text: string
  author: string
}

const RandomQuote = () => {
  const [quote, setQuote] = useState<Quote | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState<{ type: 'error' | 'info' | null; text: string }>({ type: null, text: '' })

  const fetchRandomQuote = async () => {
    setIsLoading(true)
    setMessage({ type: null, text: '' })

    try {
      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001'
      const response = await fetch(`${apiUrl}/api/quotes/random`)

      if (response.ok) {
        const data = await response.json()
        if (data.quote) {
          setQuote(data.quote)
        } else {
          setMessage({ type: 'info', text: 'No quotes found. Add some quotes first!' })
          setQuote(null)
        }
      } else if (response.status === 404) {
        setMessage({ type: 'info', text: 'No quotes found. Add some quotes first!' })
        setQuote(null)
      } else {
        const errorData = await response.json()
        setMessage({ type: 'error', text: errorData.message || 'Failed to fetch quote' })
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Network error. Please try again.' })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="random-quote">
      <h2>Random Quote</h2>
      
      <button 
        onClick={fetchRandomQuote}
        className="random-btn"
        disabled={isLoading}
      >
        {isLoading ? 'Loading...' : 'Get Random Quote'}
      </button>

      {message.type && (
        <div className={`message ${message.type}`}>
          {message.text}
        </div>
      )}

      {quote && (
        <div className="quote-display">
          <div className="quote-text">
            "{quote.text}"
          </div>
          <div className="quote-author">
            — {quote.author}
          </div>
        </div>
      )}
    </div>
  )
}

export default RandomQuote
