.App {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.App-header {
  margin-bottom: 2rem;
}

.App-header h1 {
  color: #333;
  margin-bottom: 1rem;
}

.tab-nav {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 2rem;
}

.tab-nav button {
  padding: 0.75rem 1.5rem;
  border: 2px solid #007bff;
  background: white;
  color: #007bff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.tab-nav button:hover {
  background: #007bff;
  color: white;
}

.tab-nav button.active {
  background: #007bff;
  color: white;
}

.App-main {
  min-height: 400px;
}

.quote-form {
  max-width: 500px;
  margin: 0 auto;
  padding: 2rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

.form-group {
  margin-bottom: 1rem;
  text-align: left;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: bold;
  color: #333;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  box-sizing: border-box;
}

.form-group textarea {
  min-height: 100px;
  resize: vertical;
}

.submit-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.3s ease;
}

.submit-btn:hover {
  background: #218838;
}

.submit-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.random-quote {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
}

.quote-display {
  background: #f8f9fa;
  border-left: 4px solid #007bff;
  padding: 2rem;
  margin: 2rem 0;
  border-radius: 4px;
  text-align: left;
}

.quote-text {
  font-size: 1.2rem;
  font-style: italic;
  margin-bottom: 1rem;
  line-height: 1.6;
}

.quote-author {
  font-weight: bold;
  color: #666;
  text-align: right;
}

.random-btn {
  background: #17a2b8;
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.3s ease;
}

.random-btn:hover {
  background: #138496;
}

.random-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.message {
  padding: 1rem;
  border-radius: 4px;
  margin: 1rem 0;
}

.message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.message.info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}
