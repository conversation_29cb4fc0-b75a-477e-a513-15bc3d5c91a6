import { useState } from 'react'

interface Quote {
  text: string
  author: string
}

const QuoteForm = () => {
  const [quote, setQuote] = useState<Quote>({ text: '', author: '' })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error' | null; text: string }>({ type: null, text: '' })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!quote.text.trim() || !quote.author.trim()) {
      setMessage({ type: 'error', text: 'Please fill in both quote text and author' })
      return
    }

    setIsSubmitting(true)
    setMessage({ type: null, text: '' })

    try {
      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001'
      const response = await fetch(`${apiUrl}/api/quotes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(quote),
      })

      if (response.ok) {
        setMessage({ type: 'success', text: 'Quote saved successfully!' })
        setQuote({ text: '', author: '' })
      } else {
        const errorData = await response.json()
        setMessage({ type: 'error', text: errorData.message || 'Failed to save quote' })
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Network error. Please try again.' })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setQuote(prev => ({ ...prev, [name]: value }))
  }

  return (
    <div className="quote-form">
      <h2>Add a New Quote</h2>
      
      {message.type && (
        <div className={`message ${message.type}`}>
          {message.text}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="text">Quote Text:</label>
          <textarea
            id="text"
            name="text"
            value={quote.text}
            onChange={handleChange}
            placeholder="Enter the quote text..."
            required
          />
        </div>

        <div className="form-group">
          <label htmlFor="author">Author:</label>
          <input
            type="text"
            id="author"
            name="author"
            value={quote.author}
            onChange={handleChange}
            placeholder="Enter the author's name..."
            required
          />
        </div>

        <button 
          type="submit" 
          className="submit-btn"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Saving...' : 'Save Quote'}
        </button>
      </form>
    </div>
  )
}

export default QuoteForm
