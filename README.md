# Quote Management Application

A full-stack application for managing and displaying quotes, built with React, TypeScript, and Node.js, containerized with Docker.

## Features

- **Add Quotes**: Enter quote text and author to save in the database
- **Random Quote Display**: Click a button to display a random quote from the collection

## Tech Stack

- **Frontend**: React + TypeScript + Vite
- **Backend**: Node.js + Express + TypeScript
- **Database**: SQLite
- **Containerization**: Docker + Docker Compose

## Getting Started

### Prerequisites

- Docker
- Docker Compose

### Running the Application

1. Clone this repository
2. Navigate to the project directory
3. Run the application:

```bash
docker-compose up --build
```

4. Open your browser and navigate to:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:3001

### Development

The application is set up with hot reload for both frontend and backend development.

## API Endpoints

- `POST /api/quotes` - Save a new quote
- `GET /api/quotes/random` - Get a random quote

## Project Structure

```
├── frontend/          # React + TypeScript frontend
├── backend/           # Node.js + Express backend
├── docker-compose.yml # Docker Compose configuration
└── README.md         # This file
```
