#!/bin/bash

echo "=== Quote Management App - Setup Test ==="
echo

# Check if Docker is available
echo "1. Checking Docker availability..."
if command -v docker &> /dev/null; then
    echo "✓ Docker is installed"
    if docker ps &> /dev/null; then
        echo "✓ Docker daemon is running"
        DOCKER_AVAILABLE=true
    else
        echo "✗ Docker daemon is not running or not accessible"
        DOCKER_AVAILABLE=false
    fi
else
    echo "✗ Docker is not installed"
    DOCKER_AVAILABLE=false
fi

echo

# Check project structure
echo "2. Checking project structure..."
if [ -f "docker-compose.yml" ]; then
    echo "✓ docker-compose.yml exists"
else
    echo "✗ docker-compose.yml missing"
fi

if [ -d "frontend" ] && [ -f "frontend/package.json" ]; then
    echo "✓ Frontend structure exists"
else
    echo "✗ Frontend structure missing"
fi

if [ -d "backend" ] && [ -f "backend/package.json" ]; then
    echo "✓ Backend structure exists"
else
    echo "✗ Backend structure missing"
fi

echo

# Check key files
echo "3. Checking key application files..."
FILES=(
    "frontend/src/App.tsx"
    "frontend/src/components/QuoteForm.tsx"
    "frontend/src/components/RandomQuote.tsx"
    "backend/src/index.ts"
    "backend/src/database.ts"
    "backend/src/routes/quotes.ts"
)

for file in "${FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✓ $file exists"
    else
        echo "✗ $file missing"
    fi
done

echo

if [ "$DOCKER_AVAILABLE" = true ]; then
    echo "4. Ready to run with Docker!"
    echo "   Run: docker compose up --build"
    echo "   Then visit: http://localhost:3000"
else
    echo "4. Docker not available. Alternative setup:"
    echo "   Install Node.js and npm, then:"
    echo "   cd backend && npm install && npm run dev"
    echo "   cd frontend && npm install && npm run dev"
fi

echo
echo "=== Setup Test Complete ==="
